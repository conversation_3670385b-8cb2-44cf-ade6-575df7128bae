/**
 * Utility functions for WebSocket connection handling
 */

export interface WebSocketStatus {
  connected: boolean;
  connecting?: boolean;
  error?: string | null;
  reconnectAttempt?: number;
}

/**
 * Waits for WebSocket connection to be established with a timeout
 * @param getStatus - Function that returns current WebSocket status
 * @param maxWaitTime - Maximum time to wait in milliseconds (default: 5000)
 * @returns Promise<boolean> - true if connected, false if timeout or error
 */
export const waitForWebSocketConnection = async (
  getStatus: () => WebSocketStatus,
  maxWaitTime: number = 5000
): Promise<boolean> => {
  const startTime = Date.now();
  let lastStatus: WebSocketStatus | null = null;

  console.debug('Starting WebSocket connection wait, maxWaitTime:', maxWaitTime);

  while (Date.now() - startTime < maxWaitTime) {
    const currentStatus = getStatus();

    // Only log if status changed to reduce noise
    if (!lastStatus || JSON.stringify(lastStatus) !== JSON.stringify(currentStatus)) {
      console.debug('WebSocket status check:', currentStatus);
      lastStatus = currentStatus;
    }

    if (currentStatus.connected) {
      console.debug('WebSocket connection confirmed');
      return true;
    }

    // If there's a connection error, don't wait
    if (currentStatus.error) {
      console.error('WebSocket connection error detected:', currentStatus.error);
      return false;
    }

    // Wait 100ms before checking again (reduced back to 100ms for faster detection)
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  const finalStatus = getStatus();
  console.warn('WebSocket connection timeout after', maxWaitTime, 'ms. Final status:', finalStatus);
  return false;
};

/**
 * Creates a connection error toast configuration
 */
export const createConnectionErrorToast = () => ({
  title: 'Connection Error',
  description: 'Chat service is not available. Please try again later.',
  variant: 'destructive' as const,
});
