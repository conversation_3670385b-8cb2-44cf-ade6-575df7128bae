#!/usr/bin/env node
/**
 * Simple WebSocket Connection Test
 * Tests basic WebSocket connectivity without authentication
 */

const WebSocket = require('ws');

const wsUrl = 'ws://localhost:8004/ws';

console.log('Testing WebSocket connection to:', wsUrl);

// Create WebSocket connection without authentication to see what happens
const ws = new WebSocket(wsUrl);

ws.on('open', () => {
  console.log('✓ WebSocket connection opened');
});

ws.on('message', (data) => {
  try {
    const message = JSON.parse(data.toString());
    console.log('📨 Received message:', message);
  } catch (err) {
    console.log('📨 Received raw data:', data.toString());
  }
});

ws.on('error', (error) => {
  console.error('❌ WebSocket error:', error.message);
});

ws.on('close', (code, reason) => {
  console.log(`🔌 WebSocket closed: code=${code}, reason=${reason || 'none'}`);
  process.exit(0);
});

// Set a timeout to close the connection after 10 seconds
setTimeout(() => {
  console.log('⏰ Test timeout, closing connection');
  ws.close();
}, 10000);

// Handle Ctrl+C
process.on('SIGINT', () => {
  console.log('\n👋 Test interrupted');
  ws.close();
  process.exit(0);
});
