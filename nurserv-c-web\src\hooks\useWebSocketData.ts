import { useState, useCallback, useRef, useEffect } from 'react';
import { WebSocketMessage } from './useWebSocket';
import {
  Conversation,
  Message,
  ConversationsResponse,
  ConversationResponse,
  MessagesResponse,
  GetConversationsParams,
  GetMessagesParams,
} from '@/store/api/chatApiSlice';

interface WebSocketDataState {
  conversations: Conversation[];
  conversationDetails: Record<string, Conversation>;
  messages: Record<string, Message[]>;
  loading: Record<string, boolean>;
  errors: Record<string, string | null>;
}

interface UseWebSocketDataOptions {
  sendMessage: (message: WebSocketMessage) => boolean;
  onMessage?: (message: WebSocketMessage) => void;
}

export const useWebSocketData = ({ sendMessage, onMessage }: UseWebSocketDataOptions) => {
  const [state, setState] = useState<WebSocketDataState>({
    conversations: [],
    conversationDetails: {},
    messages: {},
    loading: {},
    errors: {},
  });

  const pendingRequests = useRef<Map<string, { resolve: (value: any) => void; reject: (error: any) => void }>>(new Map());
  const requestIdCounter = useRef(0);

  const generateRequestId = useCallback(() => {
    return `req_${Date.now()}_${++requestIdCounter.current}`;
  }, []);

  // Handle incoming WebSocket messages
  const handleWebSocketMessage = useCallback((message: WebSocketMessage) => {
    const { type, requestId, success, error, data } = message;

    // Handle responses to our requests
    if (requestId && pendingRequests.current.has(requestId)) {
      const { resolve, reject } = pendingRequests.current.get(requestId)!;
      pendingRequests.current.delete(requestId);

      if (success) {
        resolve(data);
      } else {
        reject(new Error(error || 'Request failed'));
      }
      return;
    }

    // Handle real-time updates
    switch (type) {
      case 'GET_CONVERSATIONS_RESPONSE':
        if (success && data) {
          setState(prev => ({
            ...prev,
            conversations: data.conversations || [],
            loading: { ...prev.loading, conversations: false },
            errors: { ...prev.errors, conversations: null },
          }));
        } else {
          setState(prev => ({
            ...prev,
            loading: { ...prev.loading, conversations: false },
            errors: { ...prev.errors, conversations: error || 'Failed to fetch conversations' },
          }));
        }
        break;

      case 'GET_CONVERSATION_RESPONSE':
        if (success && data?.conversation) {
          setState(prev => ({
            ...prev,
            conversationDetails: {
              ...prev.conversationDetails,
              [data.conversation.id]: data.conversation,
            },
            loading: { ...prev.loading, [`conversation_${data.conversation.id}`]: false },
            errors: { ...prev.errors, [`conversation_${data.conversation.id}`]: null },
          }));
        }
        break;

      case 'GET_MESSAGES_RESPONSE':
        if (success && data?.messages && message.conversationId) {
          setState(prev => ({
            ...prev,
            messages: {
              ...prev.messages,
              [message.conversationId!]: data.messages,
            },
            loading: { ...prev.loading, [`messages_${message.conversationId}`]: false },
            errors: { ...prev.errors, [`messages_${message.conversationId}`]: null },
          }));
        }
        break;

      case 'CREATE_CONVERSATION_RESPONSE':
        if (success && data?.conversation) {
          setState(prev => ({
            ...prev,
            conversations: [data.conversation, ...prev.conversations],
            conversationDetails: {
              ...prev.conversationDetails,
              [data.conversation.id]: data.conversation,
            },
          }));
        }
        break;

      case 'CONVERSATION_UPDATED':
        if (data?.conversation) {
          setState(prev => ({
            ...prev,
            conversations: prev.conversations.map(conv =>
              conv.id === data.conversation.id ? data.conversation : conv
            ),
            conversationDetails: {
              ...prev.conversationDetails,
              [data.conversation.id]: data.conversation,
            },
          }));
        }
        break;

      case 'MESSAGE_RECEIVED':
        if (data?.message && message.conversationId) {
          setState(prev => ({
            ...prev,
            messages: {
              ...prev.messages,
              [message.conversationId!]: [
                ...(prev.messages[message.conversationId!] || []),
                data.message,
              ],
            },
          }));
        }
        break;

      case 'MESSAGES_READ':
        if (message.conversationId) {
          setState(prev => ({
            ...prev,
            messages: {
              ...prev.messages,
              [message.conversationId!]: (prev.messages[message.conversationId!] || []).map(msg => ({
                ...msg,
                status: msg.senderId !== message.senderId ? 'read' : msg.status,
              })),
            },
          }));
        }
        break;
    }

    // Call the external onMessage handler if provided
    if (onMessage) {
      onMessage(message);
    }
  }, [onMessage]);

  // API operation functions
  const getConversations = useCallback(async (params: GetConversationsParams = {}): Promise<ConversationsResponse> => {
    const requestId = generateRequestId();
    
    setState(prev => ({
      ...prev,
      loading: { ...prev.loading, conversations: true },
      errors: { ...prev.errors, conversations: null },
    }));

    return new Promise((resolve, reject) => {
      pendingRequests.current.set(requestId, { resolve, reject });

      const success = sendMessage({
        type: 'GET_CONVERSATIONS_REQUEST',
        requestId,
        page: params.page,
        limit: params.limit,
        status: params.status,
      });

      if (!success) {
        pendingRequests.current.delete(requestId);
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, conversations: false },
          errors: { ...prev.errors, conversations: 'Failed to send request' },
        }));
        reject(new Error('Failed to send WebSocket message'));
      }
    });
  }, [sendMessage, generateRequestId]);

  const getConversation = useCallback(async (conversationId: string): Promise<ConversationResponse> => {
    const requestId = generateRequestId();
    
    setState(prev => ({
      ...prev,
      loading: { ...prev.loading, [`conversation_${conversationId}`]: true },
      errors: { ...prev.errors, [`conversation_${conversationId}`]: null },
    }));

    return new Promise((resolve, reject) => {
      pendingRequests.current.set(requestId, { resolve, reject });

      const success = sendMessage({
        type: 'GET_CONVERSATION_REQUEST',
        requestId,
        conversationId,
      });

      if (!success) {
        pendingRequests.current.delete(requestId);
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, [`conversation_${conversationId}`]: false },
          errors: { ...prev.errors, [`conversation_${conversationId}`]: 'Failed to send request' },
        }));
        reject(new Error('Failed to send WebSocket message'));
      }
    });
  }, [sendMessage, generateRequestId]);

  const getMessages = useCallback(async (params: GetMessagesParams): Promise<MessagesResponse> => {
    const requestId = generateRequestId();
    const { conversationId } = params;
    
    setState(prev => ({
      ...prev,
      loading: { ...prev.loading, [`messages_${conversationId}`]: true },
      errors: { ...prev.errors, [`messages_${conversationId}`]: null },
    }));

    return new Promise((resolve, reject) => {
      pendingRequests.current.set(requestId, { resolve, reject });

      const success = sendMessage({
        type: 'GET_MESSAGES_REQUEST',
        requestId,
        conversationId,
        page: params.page,
        limit: params.limit,
      });

      if (!success) {
        pendingRequests.current.delete(requestId);
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, [`messages_${conversationId}`]: false },
          errors: { ...prev.errors, [`messages_${conversationId}`]: 'Failed to send request' },
        }));
        reject(new Error('Failed to send WebSocket message'));
      }
    });
  }, [sendMessage, generateRequestId]);

  const createConversation = useCallback(async (data: { nurseId: string; nurseName: string; initialMessage?: string }) => {
    const requestId = generateRequestId();

    return new Promise((resolve, reject) => {
      // Add a small delay to ensure WebSocket is fully connected
      setTimeout(() => {
        pendingRequests.current.set(requestId, { resolve, reject });

        // Set a timeout for the request
        const timeoutId = setTimeout(() => {
          if (pendingRequests.current.has(requestId)) {
            pendingRequests.current.delete(requestId);
            reject(new Error('Request timeout. Please try again.'));
          }
        }, 10000); // 10 second timeout

        const success = sendMessage({
          type: 'CREATE_CONVERSATION_REQUEST',
          requestId,
          data,
        });

        if (!success) {
          clearTimeout(timeoutId);
          pendingRequests.current.delete(requestId);
          console.error('WebSocket send failed, connection status may be invalid');
          reject(new Error('WebSocket is not connected. Please check your connection and try again.'));
        }
      }, 500); // Add a 500ms delay to ensure WebSocket is ready
    });
  }, [sendMessage, generateRequestId]);

  const markMessagesAsRead = useCallback(async (conversationId: string) => {
    const requestId = generateRequestId();

    return new Promise((resolve, reject) => {
      pendingRequests.current.set(requestId, { resolve, reject });

      const success = sendMessage({
        type: 'MARK_MESSAGES_READ_REQUEST',
        requestId,
        conversationId,
      });

      if (!success) {
        pendingRequests.current.delete(requestId);
        reject(new Error('Failed to send WebSocket message'));
      }
    });
  }, [sendMessage, generateRequestId]);

  const updateConversationStatus = useCallback(async (conversationId: string, status: 'active' | 'inactive' | 'archived') => {
    const requestId = generateRequestId();

    return new Promise((resolve, reject) => {
      pendingRequests.current.set(requestId, { resolve, reject });

      const success = sendMessage({
        type: 'UPDATE_CONVERSATION_STATUS_REQUEST',
        requestId,
        conversationId,
        status,
      });

      if (!success) {
        pendingRequests.current.delete(requestId);
        reject(new Error('Failed to send WebSocket message'));
      }
    });
  }, [sendMessage, generateRequestId]);

  // Cleanup pending requests on unmount
  useEffect(() => {
    return () => {
      pendingRequests.current.clear();
    };
  }, []);

  return {
    // State
    conversations: state.conversations,
    conversationDetails: state.conversationDetails,
    messages: state.messages,
    loading: state.loading,
    errors: state.errors,
    
    // Operations
    getConversations,
    getConversation,
    getMessages,
    createConversation,
    markMessagesAsRead,
    updateConversationStatus,
    
    // Message handler
    handleWebSocketMessage,
  };
};
